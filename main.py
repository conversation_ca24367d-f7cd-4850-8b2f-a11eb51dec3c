"""
图像翻译应用程序主界面
基于Tkinter的GUI应用程序
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
from PIL import Image, ImageTk
import cv2
import numpy as np
import threading
import os
import logging
from typing import List

# 导入自定义模块
from config import Config
from ocr_engine import OCREngine
from translator import Translator
from image_processor import ImageProcessor

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ImageTranslatorApp:
    def __init__(self, root):
        """初始化应用程序"""
        self.root = root
        self.root.title("图像翻译工具")
        self.root.geometry("1200x800")
        
        # 初始化组件
        self.config = Config()
        self.ocr_engine = OCREngine()
        self.translator = None
        self.image_processor = ImageProcessor()
        
        # 应用状态
        self.current_image_path = None
        self.current_image = None
        self.translated_image = None
        self.original_text_boxes = []
        self.translated_text_boxes = []
        self.is_processing = False
        
        # 创建界面
        self.create_widgets()
        self.setup_layout()
        
        # 加载配置
        self.load_settings()
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        self.main_frame = ttk.Frame(self.root)
        
        # 顶部工具栏
        self.toolbar_frame = ttk.Frame(self.main_frame)
        
        # 文件选择按钮
        self.select_file_btn = ttk.Button(
            self.toolbar_frame, 
            text="选择图像", 
            command=self.select_image_file
        )
        
        # 语言选择
        self.language_label = ttk.Label(self.toolbar_frame, text="目标语言:")
        self.language_var = tk.StringVar()
        self.language_combo = ttk.Combobox(
            self.toolbar_frame, 
            textvariable=self.language_var,
            values=self.config.get_supported_languages(),
            state="readonly",
            width=15
        )
        self.language_combo.set("Chinese")
        
        # API设置框架
        self.api_frame = ttk.LabelFrame(self.toolbar_frame, text="API设置", padding=10)
        
        # API端点
        self.endpoint_label = ttk.Label(self.api_frame, text="API端点:")
        self.endpoint_var = tk.StringVar()
        self.endpoint_entry = ttk.Entry(self.api_frame, textvariable=self.endpoint_var, width=40)
        
        # API密钥
        self.api_key_label = ttk.Label(self.api_frame, text="API密钥:")
        self.api_key_var = tk.StringVar()
        self.api_key_entry = ttk.Entry(self.api_frame, textvariable=self.api_key_var, show="*", width=40)
        
        # 验证API按钮
        self.validate_api_btn = ttk.Button(
            self.api_frame, 
            text="验证API", 
            command=self.validate_api_key
        )
        
        # 操作按钮
        self.translate_btn = ttk.Button(
            self.toolbar_frame, 
            text="开始翻译", 
            command=self.start_translation,
            state="disabled"
        )
        
        self.save_btn = ttk.Button(
            self.toolbar_frame, 
            text="保存结果", 
            command=self.save_result,
            state="disabled"
        )
        
        self.clear_btn = ttk.Button(
            self.toolbar_frame, 
            text="清除", 
            command=self.clear_all
        )
        
        # 主内容区域
        self.content_frame = ttk.Frame(self.main_frame)
        
        # 左侧图像显示区域
        self.image_frame = ttk.LabelFrame(self.content_frame, text="图像预览", padding=10)
        
        # 图像显示标签
        self.image_label = ttk.Label(self.image_frame, text="请选择图像文件", anchor="center")
        
        # 右侧文本区域
        self.text_frame = ttk.LabelFrame(self.content_frame, text="文本内容", padding=10)
        
        # 原文显示
        self.original_text_label = ttk.Label(self.text_frame, text="提取的原文:")
        self.original_text = scrolledtext.ScrolledText(
            self.text_frame, 
            height=10, 
            width=40,
            wrap=tk.WORD
        )
        
        # 译文显示
        self.translated_text_label = ttk.Label(self.text_frame, text="翻译结果:")
        self.translated_text = scrolledtext.ScrolledText(
            self.text_frame, 
            height=10, 
            width=40,
            wrap=tk.WORD
        )
        
        # 底部状态栏
        self.status_frame = ttk.Frame(self.main_frame)
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        self.status_label = ttk.Label(self.status_frame, textvariable=self.status_var)
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            self.status_frame, 
            variable=self.progress_var,
            mode='determinate'
        )
    
    def setup_layout(self):
        """设置界面布局"""
        # 主框架
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 工具栏布局 - 分为两行
        self.toolbar_frame.pack(fill=tk.X, pady=(0, 10))

        # 第一行：文件选择和语言选择
        toolbar_row1 = ttk.Frame(self.toolbar_frame)
        toolbar_row1.pack(fill=tk.X, pady=(0, 5))

        self.select_file_btn.pack(side=tk.LEFT, padx=(0, 10), in_=toolbar_row1)
        self.language_label.pack(side=tk.LEFT, padx=(0, 5), in_=toolbar_row1)
        self.language_combo.pack(side=tk.LEFT, padx=(0, 20), in_=toolbar_row1)

        # 操作按钮
        self.translate_btn.pack(side=tk.LEFT, padx=(0, 10), in_=toolbar_row1)
        self.save_btn.pack(side=tk.LEFT, padx=(0, 10), in_=toolbar_row1)
        self.clear_btn.pack(side=tk.LEFT, in_=toolbar_row1)

        # 第二行：API设置
        toolbar_row2 = ttk.Frame(self.toolbar_frame)
        toolbar_row2.pack(fill=tk.X, pady=(5, 0))

        # API设置框架
        self.api_frame.pack(fill=tk.X, in_=toolbar_row2)

        # API设置内部布局 - 水平排列
        api_container = ttk.Frame(self.api_frame)
        api_container.pack(fill=tk.X, padx=5, pady=5)

        # API端点
        self.endpoint_label.pack(side=tk.LEFT, in_=api_container)
        self.endpoint_entry.pack(side=tk.LEFT, padx=(5, 15), in_=api_container)

        # API密钥
        self.api_key_label.pack(side=tk.LEFT, in_=api_container)
        self.api_key_entry.pack(side=tk.LEFT, padx=(5, 15), in_=api_container)

        # 验证按钮
        self.validate_api_btn.pack(side=tk.LEFT, in_=api_container)

        # 主内容区域
        self.content_frame.pack(fill=tk.BOTH, expand=True)

        # 图像和文本区域并排布局
        self.image_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        self.text_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(10, 0))

        # 图像显示
        self.image_label.pack(fill=tk.BOTH, expand=True)

        # 文本区域布局
        self.original_text_label.pack(anchor=tk.W, pady=(0, 5))
        self.original_text.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        self.translated_text_label.pack(anchor=tk.W, pady=(0, 5))
        self.translated_text.pack(fill=tk.BOTH, expand=True)

        # 状态栏
        self.status_frame.pack(fill=tk.X, pady=(10, 0))
        self.status_label.pack(side=tk.LEFT)
        self.progress_bar.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(20, 0))
    
    def load_settings(self):
        """加载应用设置"""
        try:
            # 加载API设置
            endpoint = self.config.get('API', 'endpoint')
            self.endpoint_var.set(endpoint)
            
            # 设置窗口大小
            width = int(self.config.get('UI', 'window_width', '1200'))
            height = int(self.config.get('UI', 'window_height', '800'))
            self.root.geometry(f"{width}x{height}")
            
            logger.info("应用设置加载完成")
            
        except Exception as e:
            logger.error(f"加载设置失败: {str(e)}")
    
    def save_settings(self):
        """保存应用设置"""
        try:
            # 保存API设置
            self.config.set('API', 'endpoint', self.endpoint_var.get())
            
            # 保存窗口大小
            geometry = self.root.geometry()
            width, height = geometry.split('+')[0].split('x')
            self.config.set('UI', 'window_width', width)
            self.config.set('UI', 'window_height', height)
            
            logger.info("应用设置已保存")
            
        except Exception as e:
            logger.error(f"保存设置失败: {str(e)}")

    def select_image_file(self):
        """选择图像文件"""
        file_types = [
            ("图像文件", "*.jpg *.jpeg *.png *.bmp *.tiff *.webp"),
            ("JPEG文件", "*.jpg *.jpeg"),
            ("PNG文件", "*.png"),
            ("所有文件", "*.*")
        ]

        file_path = filedialog.askopenfilename(
            title="选择图像文件",
            filetypes=file_types
        )

        if file_path:
            self.load_image(file_path)

    def load_image(self, file_path: str):
        """加载并显示图像"""
        try:
            self.update_status("正在加载图像...")

            # 验证图像文件
            if not self.image_processor.is_valid_image(file_path):
                messagebox.showerror("错误", "无效的图像文件格式")
                return

            # 加载图像
            self.current_image = self.image_processor.load_image(file_path)
            self.current_image_path = file_path

            # 调整图像尺寸以适应显示
            display_image = self.image_processor.resize_image(self.current_image, 600, 400)

            # 转换为PIL格式并显示
            pil_image = Image.fromarray(cv2.cvtColor(display_image, cv2.COLOR_BGR2RGB))
            photo = ImageTk.PhotoImage(pil_image)

            self.image_label.configure(image=photo, text="")
            self.image_label.image = photo  # 保持引用

            # 启用翻译按钮
            self.translate_btn.configure(state="normal")

            self.update_status(f"图像已加载: {os.path.basename(file_path)}")
            logger.info(f"图像加载成功: {file_path}")

        except Exception as e:
            error_msg = f"图像加载失败: {str(e)}"
            messagebox.showerror("错误", error_msg)
            logger.error(error_msg)
            self.update_status("图像加载失败")

    def validate_api_key(self):
        """验证API密钥"""
        api_key = self.api_key_var.get().strip()
        endpoint = self.endpoint_var.get().strip()

        if not api_key:
            messagebox.showwarning("警告", "请输入API密钥")
            return

        if not endpoint:
            messagebox.showwarning("警告", "请输入API端点")
            return

        try:
            self.update_status("正在验证API密钥...")

            # 创建翻译器实例
            translator = Translator(api_key, endpoint)

            # 在后台线程中验证
            def validate_thread():
                try:
                    is_valid = translator.validate_api_key()

                    # 在主线程中更新UI
                    self.root.after(0, lambda: self.on_api_validation_complete(is_valid, translator))

                except Exception as e:
                    self.root.after(0, lambda: self.on_api_validation_error(str(e)))

            threading.Thread(target=validate_thread, daemon=True).start()

        except Exception as e:
            error_msg = f"API验证失败: {str(e)}"
            messagebox.showerror("错误", error_msg)
            self.update_status("API验证失败")

    def on_api_validation_complete(self, is_valid: bool, translator: Translator):
        """API验证完成回调"""
        if is_valid:
            self.translator = translator
            messagebox.showinfo("成功", "API密钥验证成功")
            self.update_status("API密钥验证成功")

            # 启用翻译按钮（如果有图像）
            if self.current_image is not None:
                self.translate_btn.configure(state="normal")
        else:
            messagebox.showerror("错误", "API密钥验证失败，请检查密钥和端点设置")
            self.update_status("API密钥验证失败")

    def on_api_validation_error(self, error_msg: str):
        """API验证错误回调"""
        messagebox.showerror("错误", f"API验证出错: {error_msg}")
        self.update_status("API验证出错")

    def start_translation(self):
        """开始翻译过程"""
        if self.current_image is None:
            messagebox.showwarning("警告", "请先选择图像文件")
            return

        if self.translator is None:
            messagebox.showwarning("警告", "请先验证API密钥")
            return

        if self.is_processing:
            messagebox.showinfo("提示", "正在处理中，请稍候...")
            return

        # 在后台线程中执行翻译
        threading.Thread(target=self.translation_worker, daemon=True).start()

    def translation_worker(self):
        """翻译工作线程"""
        try:
            self.is_processing = True
            self.root.after(0, lambda: self.update_status("正在提取文本..."))
            self.root.after(0, lambda: self.progress_var.set(10))

            # OCR文本提取（带位置信息）
            text_boxes = self.ocr_engine.extract_text_with_boxes(self.current_image_path)

            if not text_boxes:
                self.root.after(0, lambda: messagebox.showwarning("警告", "未能从图像中提取到文本"))
                return

            # 提取所有文本内容
            all_text = " ".join([text for text, _ in text_boxes])

            # 更新原文显示
            self.root.after(0, lambda: self.original_text.delete(1.0, tk.END))
            self.root.after(0, lambda: self.original_text.insert(1.0, all_text))

            self.root.after(0, lambda: self.update_status("正在翻译文本..."))
            self.root.after(0, lambda: self.progress_var.set(30))

            # 逐个翻译文本框
            target_language = self.language_var.get()
            translated_boxes = []

            for i, (text, bbox) in enumerate(text_boxes):
                if text.strip():
                    try:
                        translated = self.translator.translate_text(text, target_language)
                        translated_boxes.append((text, translated, bbox))

                        # 更新进度
                        progress = 30 + (i + 1) / len(text_boxes) * 50
                        self.root.after(0, lambda p=progress: self.progress_var.set(p))

                    except Exception as e:
                        logger.warning(f"翻译文本 '{text}' 失败: {str(e)}")
                        translated_boxes.append((text, text, bbox))  # 保持原文

            # 保存翻译结果
            self.translated_text_boxes = translated_boxes

            # 更新译文显示
            all_translated = " ".join([translated for _, translated, _ in translated_boxes])
            self.root.after(0, lambda: self.translated_text.delete(1.0, tk.END))
            self.root.after(0, lambda: self.translated_text.insert(1.0, all_translated))

            self.root.after(0, lambda: self.update_status("正在生成翻译图像..."))
            self.root.after(0, lambda: self.progress_var.set(80))

            # 生成带翻译文本的图像
            translated_image = self.image_processor.overlay_text_on_image(
                self.current_image.copy(),
                translated_boxes
            )

            # 显示翻译后的图像
            self.display_translated_image(translated_image)

            self.root.after(0, lambda: self.update_status("翻译完成"))
            self.root.after(0, lambda: self.progress_var.set(100))
            self.root.after(0, lambda: self.save_btn.configure(state="normal"))

            logger.info("翻译过程完成")

        except Exception as e:
            error_msg = f"翻译失败: {str(e)}"
            self.root.after(0, lambda: messagebox.showerror("错误", error_msg))
            self.root.after(0, lambda: self.update_status("翻译失败"))
            logger.error(error_msg)

        finally:
            self.is_processing = False
            self.root.after(0, lambda: self.progress_var.set(0))

    def display_translated_image(self, translated_image):
        """显示翻译后的图像"""
        try:
            # 调整图像尺寸以适应显示
            display_image = self.image_processor.resize_image(translated_image, 600, 400)

            # 转换为PIL格式并显示
            pil_image = Image.fromarray(cv2.cvtColor(display_image, cv2.COLOR_BGR2RGB))
            photo = ImageTk.PhotoImage(pil_image)

            self.image_label.configure(image=photo, text="")
            self.image_label.image = photo  # 保持引用

            # 保存翻译后的图像用于保存
            self.translated_image = translated_image

        except Exception as e:
            logger.error(f"显示翻译图像失败: {str(e)}")

    def save_result(self):
        """保存翻译结果"""
        if not hasattr(self, 'translated_image') or self.translated_image is None:
            messagebox.showwarning("警告", "没有翻译后的图像可保存")
            return

        # 选择保存位置
        file_types = [
            ("PNG文件", "*.png"),
            ("JPEG文件", "*.jpg"),
            ("所有文件", "*.*")
        ]

        output_path = filedialog.asksaveasfilename(
            title="保存翻译后的图像",
            defaultextension=".png",
            filetypes=file_types
        )

        if output_path:
            try:
                self.update_status("正在保存结果...")

                # 保存翻译后的图像
                success = self.image_processor.save_image(self.translated_image, output_path)

                if success:
                    messagebox.showinfo("成功", f"翻译图像已保存到: {output_path}")
                    self.update_status("保存完成")
                else:
                    messagebox.showerror("错误", "保存失败")
                    self.update_status("保存失败")

            except Exception as e:
                error_msg = f"保存失败: {str(e)}"
                messagebox.showerror("错误", error_msg)
                self.update_status("保存失败")
                logger.error(error_msg)

    def create_text_image(self, original_text: str, translated_text: str) -> np.ndarray:
        """创建包含文本的图像"""
        # 获取原图尺寸
        height, width = self.current_image.shape[:2]

        # 创建白色背景图像
        text_image = np.ones((height, width, 3), dtype=np.uint8) * 255

        # 使用PIL绘制文本
        from PIL import ImageDraw, ImageFont
        pil_image = Image.fromarray(text_image)
        draw = ImageDraw.Draw(pil_image)

        try:
            # 使用默认字体
            font = ImageFont.load_default()
        except:
            font = ImageFont.load_default()

        # 绘制原文
        y_offset = 50
        draw.text((20, y_offset), "原文:", fill=(0, 0, 0), font=font)
        y_offset += 40

        # 分行显示原文
        lines = self.wrap_text(original_text, width - 40)
        for line in lines[:10]:  # 限制行数
            draw.text((20, y_offset), line, fill=(0, 0, 0), font=font)
            y_offset += 30

        y_offset += 40
        draw.text((20, y_offset), "译文:", fill=(0, 0, 0), font=font)
        y_offset += 40

        # 分行显示译文
        lines = self.wrap_text(translated_text, width - 40)
        for line in lines[:10]:  # 限制行数
            draw.text((20, y_offset), line, fill=(0, 0, 0), font=font)
            y_offset += 30

        return np.array(pil_image)

    def wrap_text(self, text: str, max_width: int) -> List[str]:
        """文本换行处理"""
        words = text.split()
        lines = []
        current_line = ""

        for word in words:
            test_line = current_line + " " + word if current_line else word
            # 简单的字符数估算
            if len(test_line) * 8 < max_width:  # 假设每个字符8像素宽
                current_line = test_line
            else:
                if current_line:
                    lines.append(current_line)
                current_line = word

        if current_line:
            lines.append(current_line)

        return lines

    def clear_all(self):
        """清除所有内容"""
        # 清除图像
        self.current_image = None
        self.current_image_path = None
        self.translated_image = None
        self.image_label.configure(image="", text="请选择图像文件")
        self.image_label.image = None

        # 清除文本
        self.original_text.delete(1.0, tk.END)
        self.translated_text.delete(1.0, tk.END)

        # 清除文本框数据
        self.original_text_boxes = []
        self.translated_text_boxes = []

        # 重置状态
        self.translate_btn.configure(state="disabled")
        self.save_btn.configure(state="disabled")
        self.progress_var.set(0)

        self.update_status("已清除")
        logger.info("界面已清除")

    def update_status(self, message: str):
        """更新状态栏"""
        self.status_var.set(message)
        self.root.update_idletasks()

    def on_closing(self):
        """应用程序关闭时的处理"""
        try:
            # 保存设置
            self.save_settings()

            # 停止所有后台线程
            self.is_processing = False

            # 销毁窗口
            self.root.destroy()

        except Exception as e:
            logger.error(f"关闭应用时出错: {str(e)}")
            self.root.destroy()


def main():
    """主函数"""
    try:
        # 创建主窗口
        root = tk.Tk()

        # 创建应用实例
        app = ImageTranslatorApp(root)

        # 设置关闭事件处理
        root.protocol("WM_DELETE_WINDOW", app.on_closing)

        # 启动主循环
        root.mainloop()

    except Exception as e:
        logger.error(f"应用启动失败: {str(e)}")
        messagebox.showerror("错误", f"应用启动失败: {str(e)}")


if __name__ == "__main__":
    main()
