"""
OCR文本提取模块
使用pytesseract进行图像文字识别
"""

import pytesseract
import cv2
import numpy as np
from PIL import Image
from typing import List, Tuple, Optional
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OCREngine:
    def __init__(self, tesseract_path: Optional[str] = None):
        """
        初始化OCR引擎
        
        Args:
            tesseract_path: Tesseract可执行文件路径（Windows需要）
        """
        if tesseract_path:
            pytesseract.pytesseract.tesseract_cmd = tesseract_path
        
        # 默认OCR配置
        self.default_config = {
            'lang': 'eng+chi_sim',  # 支持英文和简体中文
            'psm': 6,  # 页面分割模式
            'oem': 3   # OCR引擎模式
        }
    
    def preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """
        图像预处理以提高OCR准确性
        
        Args:
            image: 输入图像
            
        Returns:
            处理后的图像
        """
        # 转换为灰度图
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image
        
        # 高斯模糊去噪
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        
        # 自适应阈值二值化
        thresh = cv2.adaptiveThreshold(
            blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
            cv2.THRESH_BINARY, 11, 2
        )
        
        # 形态学操作去除噪点
        kernel = np.ones((2, 2), np.uint8)
        cleaned = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
        
        return cleaned

    def load_image_safe(self, image_path: str) -> np.ndarray:
        """
        安全加载图像文件，支持中文路径

        Args:
            image_path: 图像文件路径

        Returns:
            图像数组
        """
        try:
            # 使用numpy和cv2的组合来处理中文路径
            with open(image_path, 'rb') as f:
                file_bytes = f.read()

            # 将字节数据转换为numpy数组
            nparr = np.frombuffer(file_bytes, np.uint8)

            # 使用cv2解码图像
            image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

            if image is None:
                # 如果cv2解码失败，尝试使用PIL
                try:
                    from PIL import Image as PILImage
                    pil_image = PILImage.open(image_path)
                    # 转换为RGB格式
                    if pil_image.mode != 'RGB':
                        pil_image = pil_image.convert('RGB')
                    # 转换为numpy数组并调整颜色通道顺序（PIL是RGB，OpenCV是BGR）
                    image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
                except Exception as pil_error:
                    logger.error(f"PIL加载也失败: {str(pil_error)}")
                    raise ValueError(f"无法读取图像文件: {image_path}")

            return image

        except Exception as e:
            logger.error(f"图像加载失败: {str(e)}")
            raise

    def extract_text(self, image_path: str, **kwargs) -> str:
        """
        从图像中提取文本
        
        Args:
            image_path: 图像文件路径
            **kwargs: OCR配置参数
            
        Returns:
            提取的文本
        """
        try:
            # 安全读取图像
            image = self.load_image_safe(image_path)

            # 图像预处理
            processed_image = self.preprocess_image(image)
            
            # 配置OCR参数
            config = self.default_config.copy()
            config.update(kwargs)
            
            # 构建tesseract配置字符串
            tesseract_config = f"--psm {config['psm']} --oem {config['oem']}"
            
            # 执行OCR
            text = pytesseract.image_to_string(
                processed_image, 
                lang=config['lang'],
                config=tesseract_config
            )
            
            logger.info(f"成功提取文本，长度: {len(text)}")
            return text.strip()
            
        except Exception as e:
            logger.error(f"OCR文本提取失败: {str(e)}")
            raise
    
    def extract_text_with_boxes(self, image_path: str, **kwargs) -> List[Tuple[str, Tuple[int, int, int, int]]]:
        """
        提取文本及其边界框位置

        Args:
            image_path: 图像文件路径
            **kwargs: OCR配置参数

        Returns:
            [(文本, (x, y, w, h)), ...] 文本和边界框列表
        """
        try:
            # 安全读取图像
            image = self.load_image_safe(image_path)

            # 图像预处理
            processed_image = self.preprocess_image(image)
            
            # 配置OCR参数
            config = self.default_config.copy()
            config.update(kwargs)
            
            # 构建tesseract配置字符串
            tesseract_config = f"--psm {config['psm']} --oem {config['oem']}"
            
            # 获取文本框数据
            data = pytesseract.image_to_data(
                processed_image,
                lang=config['lang'],
                config=tesseract_config,
                output_type=pytesseract.Output.DICT
            )
            
            # 提取有效文本和边界框
            text_boxes = []
            n_boxes = len(data['text'])
            
            for i in range(n_boxes):
                text = data['text'][i].strip()
                confidence = int(data['conf'][i])
                
                # 过滤低置信度和空文本
                if confidence > 30 and text:
                    x, y, w, h = data['left'][i], data['top'][i], data['width'][i], data['height'][i]
                    text_boxes.append((text, (x, y, w, h)))
            
            logger.info(f"成功提取 {len(text_boxes)} 个文本框")
            return text_boxes
            
        except Exception as e:
            logger.error(f"OCR文本框提取失败: {str(e)}")
            raise
    
    def get_available_languages(self) -> List[str]:
        """
        获取可用的OCR语言
        
        Returns:
            支持的语言列表
        """
        try:
            languages = pytesseract.get_languages()
            return languages
        except Exception as e:
            logger.error(f"获取OCR语言列表失败: {str(e)}")
            return ['eng', 'chi_sim']  # 返回默认语言
