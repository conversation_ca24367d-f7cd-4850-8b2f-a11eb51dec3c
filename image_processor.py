"""
图像处理模块
处理图像加载、文本叠加和保存
"""

import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFont
from typing import List, Tuple, Optional
import logging
import os

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ImageProcessor:
    def __init__(self):
        """初始化图像处理器"""
        self.supported_formats = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']
        
        # 默认字体设置
        self.default_font_size = 20
        self.default_font_color = (255, 255, 255)  # 白色
        self.default_bg_color = (0, 0, 0, 128)     # 半透明黑色背景
    
    def is_valid_image(self, file_path: str) -> bool:
        """
        检查文件是否为有效的图像格式
        
        Args:
            file_path: 图像文件路径
            
        Returns:
            True如果是有效图像，False否则
        """
        if not os.path.exists(file_path):
            return False
        
        # 检查文件扩展名
        _, ext = os.path.splitext(file_path.lower())
        if ext not in self.supported_formats:
            return False
        
        try:
            # 尝试打开图像
            with Image.open(file_path) as img:
                img.verify()
            return True
        except Exception as e:
            logger.error(f"图像验证失败: {str(e)}")
            return False
    
    def load_image(self, file_path: str) -> np.ndarray:
        """
        加载图像文件

        Args:
            file_path: 图像文件路径

        Returns:
            图像数组
        """
        if not self.is_valid_image(file_path):
            raise ValueError(f"无效的图像文件: {file_path}")

        try:
            # 使用numpy和cv2的组合来处理中文路径
            # 方法1：使用numpy读取文件字节，然后用cv2解码
            with open(file_path, 'rb') as f:
                file_bytes = f.read()

            # 将字节数据转换为numpy数组
            nparr = np.frombuffer(file_bytes, np.uint8)

            # 使用cv2解码图像
            image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

            if image is None:
                # 如果cv2解码失败，尝试使用PIL
                try:
                    from PIL import Image as PILImage
                    pil_image = PILImage.open(file_path)
                    # 转换为RGB格式
                    if pil_image.mode != 'RGB':
                        pil_image = pil_image.convert('RGB')
                    # 转换为numpy数组并调整颜色通道顺序（PIL是RGB，OpenCV是BGR）
                    image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
                except Exception as pil_error:
                    logger.error(f"PIL加载也失败: {str(pil_error)}")
                    raise ValueError(f"无法读取图像文件: {file_path}")

            logger.info(f"成功加载图像: {file_path}, 尺寸: {image.shape}")
            return image

        except Exception as e:
            logger.error(f"图像加载失败: {str(e)}")
            raise
    
    def resize_image(self, image: np.ndarray, max_width: int = 800, max_height: int = 600) -> np.ndarray:
        """
        调整图像尺寸以适应显示
        
        Args:
            image: 输入图像
            max_width: 最大宽度
            max_height: 最大高度
            
        Returns:
            调整后的图像
        """
        height, width = image.shape[:2]
        
        # 计算缩放比例
        scale_w = max_width / width
        scale_h = max_height / height
        scale = min(scale_w, scale_h, 1.0)  # 不放大图像
        
        if scale < 1.0:
            new_width = int(width * scale)
            new_height = int(height * scale)
            resized = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA)
            logger.info(f"图像已调整尺寸: {width}x{height} -> {new_width}x{new_height}")
            return resized
        
        return image
    
    def get_optimal_font_size(self, text: str, box_width: int, box_height: int) -> int:
        """
        根据文本框尺寸计算最佳字体大小
        
        Args:
            text: 文本内容
            box_width: 文本框宽度
            box_height: 文本框高度
            
        Returns:
            最佳字体大小
        """
        # 基于文本框尺寸的简单启发式算法
        base_size = min(box_width, box_height) // 3
        text_length = len(text)
        
        if text_length > 50:
            size_factor = 0.6
        elif text_length > 20:
            size_factor = 0.8
        else:
            size_factor = 1.0
        
        font_size = max(12, int(base_size * size_factor))
        return min(font_size, 48)  # 限制最大字体大小
    
    def overlay_text_on_image(self, image: np.ndarray, text_boxes: List[Tuple[str, str, Tuple[int, int, int, int]]],
                             font_path: Optional[str] = None) -> np.ndarray:
        """
        在图像上叠加翻译文本，替换原文

        Args:
            image: 原始图像
            text_boxes: [(原文, 译文, (x, y, w, h)), ...] 文本框列表
            font_path: 字体文件路径

        Returns:
            叠加文本后的图像
        """
        try:
            # 转换为PIL图像以支持更好的文本渲染
            pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
            draw = ImageDraw.Draw(pil_image)

            for original_text, translated_text, (x, y, w, h) in text_boxes:
                if not translated_text.strip():
                    continue

                # 扩展文本区域以确保完全覆盖原文
                padding = 5
                extended_x = max(0, x - padding)
                extended_y = max(0, y - padding)
                extended_w = min(pil_image.width - extended_x, w + 2 * padding)
                extended_h = min(pil_image.height - extended_y, h + 2 * padding)

                # 使用图像修复技术移除原文（用周围颜色填充）
                # 这里简化为用白色背景覆盖
                bg_color = self.get_background_color(pil_image, extended_x, extended_y, extended_w, extended_h)
                draw.rectangle((extended_x, extended_y, extended_x + extended_w, extended_y + extended_h),
                             fill=bg_color)

                # 计算最佳字体大小
                font_size = self.get_optimal_font_size(translated_text, w, h)

                try:
                    # 尝试使用指定字体
                    if font_path and os.path.exists(font_path):
                        font = ImageFont.truetype(font_path, font_size)
                    else:
                        # 使用默认字体
                        font = ImageFont.load_default()
                except Exception as e:
                    logger.warning(f"字体加载失败，使用默认字体: {str(e)}")
                    font = ImageFont.load_default()

                # 自动调整字体大小以适应文本框
                while font_size > 8:
                    bbox = draw.textbbox((0, 0), translated_text, font=font)
                    text_width = bbox[2] - bbox[0]
                    text_height = bbox[3] - bbox[1]

                    if text_width <= w * 0.9 and text_height <= h * 0.9:
                        break

                    font_size -= 1
                    try:
                        if font_path and os.path.exists(font_path):
                            font = ImageFont.truetype(font_path, font_size)
                        else:
                            font = ImageFont.load_default()
                    except:
                        font = ImageFont.load_default()

                # 计算文本位置（居中）
                bbox = draw.textbbox((0, 0), translated_text, font=font)
                text_width = bbox[2] - bbox[0]
                text_height = bbox[3] - bbox[1]

                text_x = x + (w - text_width) // 2
                text_y = y + (h - text_height) // 2

                # 确保文本在图像范围内
                text_x = max(0, min(text_x, pil_image.width - text_width))
                text_y = max(0, min(text_y, pil_image.height - text_height))

                # 绘制文本（使用黑色以确保可见性）
                text_color = self.get_text_color(bg_color)
                draw.text((text_x, text_y), translated_text, fill=text_color, font=font)

            # 转换回OpenCV格式
            result_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
            logger.info(f"成功替换 {len(text_boxes)} 个文本框")
            return result_image

        except Exception as e:
            logger.error(f"文本替换失败: {str(e)}")
            raise

    def get_background_color(self, pil_image, x, y, w, h):
        """
        获取文本区域的背景颜色

        Args:
            pil_image: PIL图像
            x, y, w, h: 文本区域坐标

        Returns:
            背景颜色
        """
        try:
            # 采样边缘像素来估算背景颜色
            samples = []

            # 采样上边缘
            for i in range(max(0, x), min(pil_image.width, x + w), max(1, w // 10)):
                if y > 0:
                    samples.append(pil_image.getpixel((i, y - 1)))

            # 采样下边缘
            for i in range(max(0, x), min(pil_image.width, x + w), max(1, w // 10)):
                if y + h < pil_image.height:
                    samples.append(pil_image.getpixel((i, y + h)))

            # 采样左边缘
            for i in range(max(0, y), min(pil_image.height, y + h), max(1, h // 10)):
                if x > 0:
                    samples.append(pil_image.getpixel((x - 1, i)))

            # 采样右边缘
            for i in range(max(0, y), min(pil_image.height, y + h), max(1, h // 10)):
                if x + w < pil_image.width:
                    samples.append(pil_image.getpixel((x + w, i)))

            if samples:
                # 计算平均颜色
                avg_r = sum(s[0] for s in samples) // len(samples)
                avg_g = sum(s[1] for s in samples) // len(samples)
                avg_b = sum(s[2] for s in samples) // len(samples)
                return (avg_r, avg_g, avg_b)
            else:
                return (255, 255, 255)  # 默认白色

        except Exception:
            return (255, 255, 255)  # 默认白色

    def get_text_color(self, bg_color):
        """
        根据背景颜色选择合适的文本颜色

        Args:
            bg_color: 背景颜色 (r, g, b)

        Returns:
            文本颜色
        """
        # 计算背景亮度
        brightness = (bg_color[0] * 0.299 + bg_color[1] * 0.587 + bg_color[2] * 0.114)

        # 根据亮度选择文本颜色
        if brightness > 128:
            return (0, 0, 0)  # 深色背景用白色文字
        else:
            return (255, 255, 255)  # 浅色背景用黑色文字
    
    def create_side_by_side_image(self, original_image: np.ndarray, translated_image: np.ndarray) -> np.ndarray:
        """
        创建原图和翻译图的并排对比图
        
        Args:
            original_image: 原始图像
            translated_image: 翻译后图像
            
        Returns:
            并排对比图像
        """
        try:
            # 确保两个图像高度相同
            h1, w1 = original_image.shape[:2]
            h2, w2 = translated_image.shape[:2]
            
            target_height = max(h1, h2)
            
            # 调整图像高度
            if h1 != target_height:
                scale = target_height / h1
                new_w1 = int(w1 * scale)
                original_image = cv2.resize(original_image, (new_w1, target_height))
                w1 = new_w1
            
            if h2 != target_height:
                scale = target_height / h2
                new_w2 = int(w2 * scale)
                translated_image = cv2.resize(translated_image, (new_w2, target_height))
                w2 = new_w2
            
            # 创建分隔线
            separator = np.ones((target_height, 5, 3), dtype=np.uint8) * 128
            
            # 水平拼接
            combined = np.hstack([original_image, separator, translated_image])
            
            logger.info(f"创建并排对比图，尺寸: {combined.shape}")
            return combined
            
        except Exception as e:
            logger.error(f"创建并排图像失败: {str(e)}")
            raise
    
    def save_image(self, image: np.ndarray, output_path: str, quality: int = 95) -> bool:
        """
        保存图像到文件
        
        Args:
            image: 要保存的图像
            output_path: 输出文件路径
            quality: JPEG质量（1-100）
            
        Returns:
            True如果保存成功，False否则
        """
        try:
            # 确保输出目录存在
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)
            
            # 根据文件扩展名设置保存参数
            _, ext = os.path.splitext(output_path.lower())
            
            if ext in ['.jpg', '.jpeg']:
                cv2.imwrite(output_path, image, [cv2.IMWRITE_JPEG_QUALITY, quality])
            elif ext == '.png':
                cv2.imwrite(output_path, image, [cv2.IMWRITE_PNG_COMPRESSION, 9])
            else:
                cv2.imwrite(output_path, image)
            
            logger.info(f"图像已保存到: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"图像保存失败: {str(e)}")
            return False
