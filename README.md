# 图像翻译工具

一个基于Python的图像翻译应用程序，支持从图像中提取文本并使用OpenAI API进行翻译。

## 功能特性

- 🖼️ 支持多种图像格式（JPG, PNG, BMP, TIFF, WebP）
- 🔍 使用OCR技术提取图像中的文本
- 🌐 支持多种语言翻译
- 🔧 可自定义OpenAI API端点
- 💾 保存翻译结果为图像文件
- 🖥️ 直观的图形用户界面

## 系统要求

- Python 3.7+
- Windows/macOS/Linux
- Tesseract OCR引擎

## 安装步骤

### 1. 安装Tesseract OCR

#### Windows:
1. 下载Tesseract安装包：https://github.com/UB-Mannheim/tesseract/wiki
2. 安装到默认路径（通常是 `C:\Program Files\Tesseract-OCR\`）
3. 将Tesseract路径添加到系统环境变量PATH中

#### macOS:
```bash
brew install tesseract
```

#### Ubuntu/Debian:
```bash
sudo apt update
sudo apt install tesseract-ocr
sudo apt install tesseract-ocr-chi-sim  # 中文支持
```

### 2. 安装Python依赖

```bash
# 克隆或下载项目文件
cd OCR_AI

# 安装依赖包
pip install -r requirements.txt
```

### 3. 配置API

1. 获取OpenAI API密钥
2. 在应用程序中输入API端点和密钥

## 使用方法

### 启动应用

```bash
python main.py
```

### 基本操作流程

1. **选择图像**：点击"选择图像"按钮，选择要翻译的图像文件

2. **配置API**：
   - 输入OpenAI API端点URL（默认：https://api.openai.com/v1/chat/completions）
   - 输入API密钥（格式：sk-xxxxxx）
   - 点击"验证API"确认设置正确

3. **选择目标语言**：从下拉菜单中选择要翻译到的语言

4. **开始翻译**：点击"开始翻译"按钮，等待处理完成

5. **查看结果**：
   - 左侧显示原始图像
   - 右侧显示提取的原文和翻译结果

6. **保存结果**：点击"保存结果"按钮，选择保存位置

## 配置文件

应用程序会自动创建 `config.ini` 配置文件，包含以下设置：

```ini
[API]
endpoint = https://api.openai.com/v1/chat/completions
model = gpt-3.5-turbo
max_tokens = 1000
temperature = 0.3

[OCR]
language = eng+chi_sim
psm = 6
oem = 3

[UI]
window_width = 1200
window_height = 800
theme = default

[LANGUAGES]
supported = English,Chinese,Japanese,Korean,French,German,Spanish,Russian
```

## 支持的语言

- English（英语）
- Chinese（中文）
- Japanese（日语）
- Korean（韩语）
- French（法语）
- German（德语）
- Spanish（西班牙语）
- Russian（俄语）

## 故障排除

### 常见问题

1. **中文路径问题**
   - 如果图像文件路径包含中文字符，可能导致加载失败
   - 解决方案：将图像文件移动到英文路径下，或使用英文文件名
   - 应用程序已优化支持中文路径，但某些系统可能仍有兼容性问题

2. **OCR识别失败**
   - 确保Tesseract正确安装
   - 检查图像质量和清晰度
   - 尝试不同的图像格式
   - 确保图像中的文字足够清晰和大小适中

3. **API连接失败**
   - 检查网络连接
   - 验证API密钥和端点设置
   - 确认API配额未超限
   - 检查防火墙设置

4. **翻译质量问题**
   - 尝试调整OCR语言设置
   - 使用更清晰的图像
   - 检查原文提取是否准确
   - 考虑手动编辑提取的文本后再翻译

5. **界面显示问题**
   - 如果API设置框架不显示，请调整窗口大小
   - 确保屏幕分辨率足够显示完整界面
   - 尝试重启应用程序

### 日志文件

应用程序会在控制台输出详细的日志信息，帮助诊断问题。

## 项目结构

```
OCR_AI/
├── main.py              # 主应用程序
├── config.py            # 配置管理
├── ocr_engine.py        # OCR文本提取
├── translator.py        # 翻译功能
├── image_processor.py   # 图像处理
├── requirements.txt     # 依赖包列表
├── README.md           # 说明文档
└── config.ini          # 配置文件（自动生成）
```

## 开发说明

### 扩展功能

- 添加更多OCR引擎支持
- 实现批量图像处理
- 添加文本编辑功能
- 支持更多翻译服务

### 贡献指南

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

本项目采用MIT许可证。

## 联系方式

如有问题或建议，请通过GitHub Issues联系。
