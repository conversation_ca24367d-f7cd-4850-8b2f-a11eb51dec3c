"""
测试图像加载功能，特别是中文路径支持
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from image_processor import ImageProcessor
from ocr_engine import OCREngine
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_image_loading():
    """测试图像加载功能"""
    print("🧪 测试图像加载功能")
    print("=" * 40)
    
    # 创建处理器实例
    image_processor = ImageProcessor()
    ocr_engine = OCREngine()
    
    # 测试文件路径（包含中文）
    test_paths = [
        "E:/Downloads/images_1688_684295012159/主图/主图_02.jpg",
        "test_image.jpg",  # 如果存在的话
    ]
    
    for test_path in test_paths:
        print(f"\n📁 测试路径: {test_path}")
        
        if not os.path.exists(test_path):
            print(f"❌ 文件不存在: {test_path}")
            continue
        
        try:
            # 测试图像处理器加载
            print("🔍 测试 ImageProcessor.load_image()...")
            image = image_processor.load_image(test_path)
            print(f"✅ ImageProcessor 加载成功，图像尺寸: {image.shape}")
            
            # 测试OCR引擎加载
            print("🔍 测试 OCREngine.load_image_safe()...")
            image2 = ocr_engine.load_image_safe(test_path)
            print(f"✅ OCREngine 加载成功，图像尺寸: {image2.shape}")
            
            # 测试OCR文本提取
            print("🔍 测试 OCR 文本提取...")
            try:
                text = ocr_engine.extract_text(test_path)
                print(f"✅ OCR 提取成功，文本长度: {len(text)}")
                if text.strip():
                    print(f"📝 提取的文本预览: {text[:100]}...")
                else:
                    print("⚠️  未提取到文本内容")
            except Exception as ocr_error:
                print(f"❌ OCR 提取失败: {str(ocr_error)}")
            
        except Exception as e:
            print(f"❌ 加载失败: {str(e)}")
    
    print("\n🎯 测试完成")

def test_path_encoding():
    """测试路径编码问题"""
    print("\n🧪 测试路径编码")
    print("=" * 40)
    
    test_path = "E:/Downloads/images_1688_684295012159/主图/主图_02.jpg"
    
    print(f"原始路径: {test_path}")
    print(f"路径编码: {test_path.encode('utf-8')}")
    print(f"路径存在: {os.path.exists(test_path)}")
    
    # 测试不同的路径表示方法
    import pathlib
    path_obj = pathlib.Path(test_path)
    print(f"Pathlib 路径: {path_obj}")
    print(f"Pathlib 存在: {path_obj.exists()}")

if __name__ == "__main__":
    try:
        test_image_loading()
        test_path_encoding()
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
    
    input("\n按回车键退出...")
