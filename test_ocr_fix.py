"""
测试OCR修复
验证load_image_safe方法是否正常工作
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ocr_engine import OCREngine
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_ocr_engine():
    """测试OCR引擎"""
    print("🧪 测试OCR引擎修复")
    print("=" * 40)
    
    try:
        # 创建OCR引擎实例
        ocr_engine = OCREngine()
        print("✅ OCREngine 实例创建成功")
        
        # 检查load_image_safe方法是否存在
        if hasattr(ocr_engine, 'load_image_safe'):
            print("✅ load_image_safe 方法存在")
        else:
            print("❌ load_image_safe 方法不存在")
            return False
        
        # 检查extract_text_with_boxes方法是否存在
        if hasattr(ocr_engine, 'extract_text_with_boxes'):
            print("✅ extract_text_with_boxes 方法存在")
        else:
            print("❌ extract_text_with_boxes 方法不存在")
            return False
        
        print("\n🎯 所有方法检查通过！")
        
        # 如果有测试图像，可以进行实际测试
        test_image_path = "test_image.jpg"  # 替换为实际的测试图像路径
        
        if os.path.exists(test_image_path):
            print(f"\n📁 发现测试图像: {test_image_path}")
            try:
                # 测试安全加载
                image = ocr_engine.load_image_safe(test_image_path)
                print(f"✅ 图像安全加载成功，尺寸: {image.shape}")
                
                # 测试文本提取
                text_boxes = ocr_engine.extract_text_with_boxes(test_image_path)
                print(f"✅ 文本框提取成功，找到 {len(text_boxes)} 个文本框")
                
                if text_boxes:
                    print("📝 提取的文本:")
                    for i, (text, bbox) in enumerate(text_boxes[:3]):  # 只显示前3个
                        print(f"   {i+1}. '{text}' at {bbox}")
                
            except Exception as e:
                print(f"⚠️  测试图像处理失败: {str(e)}")
        else:
            print(f"\n📁 未找到测试图像: {test_image_path}")
            print("   可以手动测试：将图像文件重命名为 test_image.jpg 放在当前目录")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_method_calls():
    """测试方法调用"""
    print("\n🔧 测试方法调用")
    print("=" * 30)
    
    try:
        ocr_engine = OCREngine()
        
        # 测试方法是否可以被调用（不需要实际图像）
        print("测试方法签名...")
        
        # 检查方法签名
        import inspect
        
        # load_image_safe方法
        sig = inspect.signature(ocr_engine.load_image_safe)
        print(f"✅ load_image_safe{sig}")
        
        # extract_text_with_boxes方法
        sig = inspect.signature(ocr_engine.extract_text_with_boxes)
        print(f"✅ extract_text_with_boxes{sig}")
        
        print("✅ 所有方法签名正确")
        return True
        
    except Exception as e:
        print(f"❌ 方法调用测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 OCR引擎修复验证")
    print("=" * 50)
    
    success1 = test_ocr_engine()
    success2 = test_method_calls()
    
    print("\n📊 测试结果:")
    print("=" * 30)
    print(f"OCR引擎测试: {'✅ 通过' if success1 else '❌ 失败'}")
    print(f"方法调用测试: {'✅ 通过' if success2 else '❌ 失败'}")
    
    if success1 and success2:
        print("\n🎉 所有测试通过！OCR引擎修复成功！")
        print("现在可以正常运行主应用程序了：python main.py")
    else:
        print("\n⚠️  部分测试失败，请检查错误信息")
    
    input("\n按回车键退出...")
